// Board utilities that work directly with arrays.json data
// This replaces the cartelaGenerator.ts file

// Interface for the arrays.json structure
export interface BoardData {
  id: number;
  cartela_no: number;
  bingo_numbers: (number | string)[];
}

// Convert flat array from arrays.json to column-based format for UI
// The arrays.json stores numbers in row-major order: Row1(0-4), Row2(5-9), Row3(10-14), Row4(15-19), Row5(20-24)
export const convertFlatArrayToColumnFormat = (flatArray: (number | string)[]): number[][] => {
  const board: number[][] = [[], [], [], [], []]; // 5 columns: B, I, N, G, O

  // The flat array is in row-major order, need to convert to column-based format
  // Each group of 5 numbers represents one row (B-I-N-G-O)
  for (let row = 0; row < 5; row++) {
    for (let col = 0; col < 5; col++) {
      const flatIndex = row * 5 + col; // Row-major indexing
      let value = flatArray[flatIndex];

      // Convert 'F' to 80 (FREE space)
      if (value === 'F') {
        value = 80;
      }

      board[col][row] = value as number;
    }
  }

  return board;
};

// Convert column-based board back to flat array (row-major order)
export const boardToFlatArray = (board: number[][]): number[] => {
  const flatArray: number[] = [];
  // Convert column-based board to row-wise flat array
  for (let row = 0; row < 5; row++) {
    for (let col = 0; col < 5; col++) {
      flatArray.push(board[col][row]);
    }
  }
  return flatArray;
};

// Get a specific board by cartela number from arrays data
export const getBoardByCartelaNumber = (
  cartelaNo: number,
  boardsData: BoardData[]
): { board: number[][], index: number } | null => {
  if (!boardsData || boardsData.length === 0) {
    return null;
  }

  // Search for the board with the matching cartela_no
  const boardData = boardsData.find(board => board.cartela_no === cartelaNo);

  if (!boardData) {
    return null;
  }

  // Find the index of this board in the loaded data
  const index = boardsData.indexOf(boardData);

  // Convert the flat array to column-based format
  const board = convertFlatArrayToColumnFormat(boardData.bingo_numbers);

  return {
    board: board,
    index: index
  };
};

// Get a specific board by index (0-based array index)
export const getBoardByIndex = (index: number, boardsData: BoardData[]): number[][] => {
  if (!boardsData || boardsData.length === 0) {
    throw new Error('No board data available');
  }

  if (index < 0 || index >= boardsData.length) {
    throw new Error(`Board index must be between 0 and ${boardsData.length - 1}`);
  }

  const boardData = boardsData[index];
  return convertFlatArrayToColumnFormat(boardData.bingo_numbers);
};

// Get cartela number from board index (reverse lookup)
export const getCartelaNumberFromIndex = (index: number, boardsData: BoardData[]): number => {
  if (!boardsData || boardsData.length === 0) {
    // Fallback: assume 1-based numbering
    return index + 1;
  }

  if (index < 0 || index >= boardsData.length) {
    // Fallback: assume 1-based numbering
    return index + 1;
  }

  return boardsData[index].cartela_no;
};

// Check if a cartela number is valid
export const isValidCartelaNumber = (cartelaNo: number, boardsData: BoardData[]): boolean => {
  if (!boardsData || boardsData.length === 0) {
    return false;
  }
  return boardsData.some(board => board.cartela_no === cartelaNo);
};

// Get the valid cartela number range
export const getCartelaNumberRange = (boardsData: BoardData[]): { min: number, max: number } => {
  if (!boardsData || boardsData.length === 0) {
    return { min: 1, max: 1 };
  }

  const cartelaNumbers = boardsData.map(board => board.cartela_no);
  return {
    min: Math.min(...cartelaNumbers),
    max: Math.max(...cartelaNumbers)
  };
};

// Check if a board index is valid
export const isValidBoardIndex = (index: number, boardsData: BoardData[]): boolean => {
  if (!boardsData || boardsData.length === 0) {
    return false;
  }
  return index >= 0 && index < boardsData.length;
};

// Get total number of available boards
export const getTotalBoardCount = (boardsData: BoardData[]): number => {
  if (!boardsData) {
    return 0;
  }
  return boardsData.length;
};

// Get a random board index
export const getRandomBoardIndex = (boardsData: BoardData[]): number => {
  if (!boardsData || boardsData.length === 0) {
    return 0;
  }
  return Math.floor(Math.random() * boardsData.length);
};

// Get board numbers as a formatted string (for display)
export const getBoardAsString = (index: number, boardsData: BoardData[]): string => {
  const board = getBoardByIndex(index, boardsData);
  // Convert column-based board to row-based display
  const rows: string[] = [];
  for (let row = 0; row < 5; row++) {
    const rowValues: string[] = [];
    for (let col = 0; col < 5; col++) {
      const num = board[col][row];
      rowValues.push(num === 80 ? "FREE" : num.toString().padStart(2, " "));
    }
    rows.push(rowValues.join(" | "));
  }
  return rows.join("\n");
};

// Search for boards that contain specific numbers
export const searchBoardsByNumbers = (searchNumbers: number[], boardsData: BoardData[]): number[] => {
  const matchingIndices: number[] = [];

  for (let i = 0; i < boardsData.length; i++) {
    const board = getBoardByIndex(i, boardsData);
    const flatBoard = boardToFlatArray(board);
    const hasAllNumbers = searchNumbers.every((num) => flatBoard.includes(num));

    if (hasAllNumbers) {
      matchingIndices.push(i);
    }
  }

  return matchingIndices;
};

// Get board statistics
export const getBoardStats = (
  index: number,
  boardsData: BoardData[]
): {
  index: number;
  totalNumbers: number;
  columnCounts: { B: number; I: number; N: number; G: number; O: number };
  numberRanges: { min: number; max: number };
} => {
  const board = getBoardByIndex(index, boardsData);
  const flatBoard = boardToFlatArray(board);
  const numbers = flatBoard.filter((n) => n !== 80);

  const columnCounts = {
    B: board[0].filter((n) => n !== 80).length, // B column is board[0]
    I: board[1].filter((n) => n !== 80).length, // I column is board[1]
    N: board[2].filter((n) => n !== 80).length, // N column is board[2]
    G: board[3].filter((n) => n !== 80).length, // G column is board[3]
    O: board[4].filter((n) => n !== 80).length, // O column is board[4]
  };

  return {
    index,
    totalNumbers: numbers.length,
    columnCounts,
    numberRanges: {
      min: Math.min(...numbers),
      max: Math.max(...numbers),
    },
  };
};

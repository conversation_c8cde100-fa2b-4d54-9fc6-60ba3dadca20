import { useState, useEffect } from "react";


export default function BingoBoard({ onGameUpdate }) {
  const [calledBalls, setCalledBalls] = useState(["Free"]);
  const [cartelaNumbers, setCartelaNumbers] = useState(() => {
    // Initialize with 5000 random cartelas using seed 75
    const random = new Random(75);
    return Array.from({ length: 15000 }, () =>
      Array.from({ length: 25 }, () => random.nextInt(75) + 1),
    );
  });

  const handleBallClick = (number) => {
    setCalledBalls((prev) => [...prev, number]);
    onGameUpdate(calledBalls);
  };

  // Render 75 balls in grid layout
  return (
    <div className="bingo-grid">
      {Array.from({ length: 75 }, (_, i) => (
        <div
          key={i + 1}
          className="bingo-ball"
          onClick={() => handleBallClick(i + 1)}
        >
          {i + 1}
        </div>
      ))}
    </div>
  );
}

// Seedable random number generator
class Random {
  constructor(seed) {
    this.seed = seed;
  }

  nextInt(max) {
    this.seed = (this.seed * 9301 + 49297) % 233280;
    return Math.floor((this.seed / 233280) * max);
  }
}

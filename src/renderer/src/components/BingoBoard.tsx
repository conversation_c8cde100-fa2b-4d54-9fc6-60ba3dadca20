import { motion } from "motion/react";
import { cn } from "@renderer/lib/utils";
import { useMemo } from "react";
import { getBoardByIndex, getRandomBoardIndex, type BoardData } from "../lib/arraysBoardUtils";

interface BingoBoardProps {
  drawnNumbers: number[];
  className?: string;
  boardId?: string;
  boardsData?: BoardData[];
}

// BINGO column ranges
const BINGO_RANGES = {
  B: { min: 1, max: 15, col: 0 },
  I: { min: 16, max: 30, col: 1 },
  N: { min: 31, max: 45, col: 2 },
  G: { min: 46, max: 60, col: 3 },
  O: { min: 61, max: 75, col: 4 },
};

// Generate a sample board for display using the arrays data
const generateSampleBoard = (boardId: string = "SAMPLE123", boardsData: BoardData[] = []): number[][] => {
  if (boardsData.length === 0) {
    // Return a default board if no data available
    return [
      [1, 5, 11, 12, 15],
      [17, 25, 20, 21, 23],
      [34, 32, 80, 40, 44], // 80 = FREE
      [50, 58, 46, 47, 52],
      [69, 71, 64, 63, 66]
    ];
  }

  // Convert board ID to index for consistent boards
  let seed = 75;
  for (let i = 0; i < boardId.length; i++) {
    seed += boardId.charCodeAt(i) * (i + 1);
  }

  // Use seed to get a consistent board index
  const index = seed % boardsData.length;
  try {
    return getBoardByIndex(index, boardsData);
  } catch (error) {
    console.warn(`Invalid board index ${index}, using random board`);
    const randomIndex = getRandomBoardIndex(boardsData);
    return getBoardByIndex(randomIndex, boardsData);
  }
};

export const BingoBoard: React.FC<BingoBoardProps> = ({
  drawnNumbers,
  className,
  boardId,
  boardsData = [],
}) => {
  const drawnSet = new Set(drawnNumbers);

  // Generate board based on boardId or use default
  const sampleBoard = useMemo(() => {
    return generateSampleBoard(boardId, boardsData);
  }, [boardId, boardsData]);

  const markedCount = sampleBoard
    .flat()
    .filter((num) => num !== 80 && drawnSet.has(num)).length;

  return (
    <div
      className={cn(
        "bg-gradient-to-br from-slate-800 to-slate-900 rounded-xl p-6 shadow-2xl border border-slate-600/30",
        className,
      )}
    >
      {/* Header */}
      <div className="text-center mb-6">
        <h2 className="text-3xl font-bold text-white mb-2">
          Sample Player Board
        </h2>
        <div className="text-lg text-blue-200">
          {markedCount}/24 numbers marked
        </div>
      </div>

      {/* BINGO Header */}
      <div className="grid grid-cols-5 gap-3 mb-6">
        {[
          { letter: "B", color: "bg-blue-600" },
          { letter: "I", color: "bg-purple-600" },
          { letter: "N", color: "bg-red-600" },
          { letter: "G", color: "bg-green-600" },
          { letter: "O", color: "bg-yellow-600" },
        ].map(({ letter, color }) => (
          <div
            key={letter}
            className={cn(
              "text-center text-white font-bold text-3xl rounded-lg py-3 shadow-lg",
              color,
            )}
          >
            {letter}
          </div>
        ))}
      </div>

      {/* Board Grid */}
      <div className="grid grid-cols-5 gap-3 mb-6">
        {/* Convert column-based board to row-based display */}
        {Array.from({ length: 5 }, (_, rowIndex) =>
          Array.from({ length: 5 }, (_, colIndex) => {
            // Access board data as board[col][row] since it's column-based
            const number = sampleBoard[colIndex][rowIndex];
            const isMarked = number !== 80 && drawnSet.has(number);
            const isFree = number === 80;

            return (
              <motion.div
                key={`${rowIndex}-${colIndex}`}
                className={cn(
                  "aspect-square flex items-center justify-center rounded-lg text-xl font-bold shadow-md border-2",
                  isFree
                    ? "bg-gradient-to-br from-yellow-400 to-yellow-600 text-black border-yellow-300" // FREE space
                    : isMarked
                      ? "bg-gradient-to-br from-green-500 to-green-700 text-white border-green-300" // Marked number
                      : "bg-white text-black border-gray-300 hover:bg-gray-50", // Unmarked number
                )}
                initial={{ scale: 1 }}
                animate={
                  isMarked
                    ? {
                        scale: [1, 1.1, 1],
                        boxShadow: [
                          "0 4px 15px rgba(0,0,0,0.2)",
                          "0 8px 25px rgba(34, 197, 94, 0.4)",
                          "0 4px 15px rgba(0,0,0,0.2)",
                        ],
                        transition: { duration: 0.6 },
                      }
                    : {}
                }
                whileHover={{ scale: 1.05 }}
              >
                {isFree ? (
                  <div className="text-center">
                    <div className="text-2xl">★</div>
                    <div className="text-xs font-bold">FREE</div>
                  </div>
                ) : (
                  number
                )}
              </motion.div>
            );
          }),
        ).flat()}
      </div>

      {/* Board Info */}
      <div className="bg-white/10 rounded-lg p-4 backdrop-blur-sm">
        <div className="text-center text-white">
          <div className="text-sm text-blue-200 uppercase tracking-wide mb-1">
            Sample Player Board
          </div>
          <div className="text-lg font-semibold">
            {markedCount === 24
              ? "🎉 FULL HOUSE!"
              : `${markedCount}/24 Numbers Marked`}
          </div>
          <div className="text-sm text-blue-200 mt-2">
            Players mark their own boards when numbers are called
          </div>
        </div>
      </div>
    </div>
  );
};
